import 'package:refreshed/refreshed.dart';
import 'package:crud/controllers/initial_controller.dart';
import 'package:crud/views/home_view.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart';
import 'package:sizer/sizer.dart';

void main() {
  final initialController = Get.put(InitialController());
  runApp(
    Sizer(
      builder: (context, orientation, screenType) {
        return Obx(
          () => ShadcnApp(
            title: 'Crud App',
            home: MainApp(),
            theme: ThemeData(
              colorScheme: initialController.checkTheme(),
              radius: 0.5,
            ),
          ),
        );
      },
    ),
  );
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return HomeView();
  }
}
