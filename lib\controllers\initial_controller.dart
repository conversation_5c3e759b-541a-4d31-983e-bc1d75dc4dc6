import 'package:refreshed/refreshed.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart';

class InitialController extends GetxController {
  var lightTheme = true.obs;

  changeTheme() {
    lightTheme.value = !lightTheme.value;
    checkTheme();
  }

  checkTheme() {
    if (lightTheme.value == true) {
      return ColorSchemes.lightOrange();
    } else {
      return ColorSchemes.darkOrange();
    }
  }
}
